using System.Diagnostics;
using CommandGuard.Interfaces;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

public partial class FormMain : Form
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// 记录用户操作和系统事件，便于调试和监控
    /// </summary>
    private readonly ILogger<FormMain> _logger;

    /// <summary>
    /// 人员业务服务
    /// 提供人员数据的CRUD操作功能
    /// </summary>
    private readonly IMemberService _memberService;

    /// <summary>
    /// 期号时间服务
    /// 提供期号时间管理和监控功能
    /// </summary>
    private readonly IIssueTimeService _issueTimeService;

    /// <summary>
    /// 音效服务
    /// 提供音效播放和管理功能
    /// </summary>
    private readonly ISoundService _soundService;

    /// <summary>
    /// 取消令牌源，用于控制后台任务的取消
    /// </summary>
    private readonly CancellationTokenSource? _cancellationTokenSource = new();

    #endregion

    #region 构造函数

    public FormMain(ILogger<FormMain> logger,
        IMemberService memberService,
        IIssueTimeService issueTimeService,
        ISoundService soundService)
    {
        // 初始化窗体组件（由设计器生成）
        InitializeComponent();

        // 依赖注入的服务实例
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _memberService = memberService ?? throw new ArgumentNullException(nameof(memberService));
        _issueTimeService = issueTimeService ?? throw new ArgumentNullException(nameof(issueTimeService));
        _soundService = soundService ?? throw new ArgumentNullException(nameof(soundService));
    }

    #endregion

    #region 窗体事件处理

    private async void FormMain_Load(object sender, EventArgs e)
    {
        try
        {
            _logger.LogInformation("主窗体开始加载");
            List<Member> memberList = await _memberService.GetAllMemberAsync();
            dataGridView1.DataSource = memberList;

            // 创建发放时间数据
            _logger.LogDebug("正在创建发放时间数据...");
            await _issueTimeService.CreateIssueTimeAsync(DateTime.Now);
            _logger.LogDebug("发放时间数据创建完成");

            _logger.LogInformation("主窗体加载完成,开始启动服务");

            // 启动服务
            await Task.WhenAll(
                _issueTimeService.UpdateCurrentIssueTimeAsync(_cancellationTokenSource!.Token),
                ShowTimeInfo(_cancellationTokenSource.Token)
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $@"主窗体加载时发生异常,{ex}");
            MessageBox.Show($@"加载数据时发生错误: {ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    #endregion

    private async Task ShowTimeInfo(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                // await Task.Delay(1000, cancellationToken);
                // int openTimeSeconds = _issueTimeService.OpenTimeDownDic[CommonHelper.Lottery];
                // if (openTimeSeconds >= 0)
                // {
                //     if (openTimeSeconds > Setting.Current.封盘时间)
                //     {
                //         openTimeSeconds -= Setting.Current.封盘时间;
                //         Invoke(() => { label_正在投注期数标题.Text = @"正在投注期数"; });
                //     }
                //     else
                //     {
                //         Invoke(() => { label_正在投注期数标题.Text = @"即将开盘"; });
                //     }
                //
                //     int hour = openTimeSeconds / 3600;
                //     int minute = openTimeSeconds % 3600 / 60;
                //     int second = openTimeSeconds % 60;
                //
                //     Invoke(() =>
                //     {
                //         label_正在投注期数.Text = _issueTimeService.IssueTimeNowDic[CommonHelper.Lottery].Issue;
                //         label_封盘倒计时.Text = hour.ToString().PadLeft(2, '0') + @":" + minute.ToString().PadLeft(2, '0') + @":" + second.ToString().PadLeft(2, '0');
                //     });
                // }
                // else
                // {
                //     int closeTimeSeconds = _issueTimeService.CloseTimeDownDic[CommonHelper.Lottery];
                //     int hour = closeTimeSeconds / 3600;
                //     int minute = closeTimeSeconds % 3600 / 60;
                //     int second = closeTimeSeconds % 60;
                //
                //     Invoke(() =>
                //     {
                //         label_正在投注期数标题.Text = @"正在投注期数";
                //         label_正在投注期数.Text = _issueTimeService.IssueTimeNowDic[CommonHelper.Lottery].Issue;
                //         label_封盘倒计时.Text = hour.ToString().PadLeft(2, '0') + @":" + minute.ToString().PadLeft(2, '0') + @":" + second.ToString().PadLeft(2, '0');
                //     });
                // }


                // 注释：原有的显示时间逻辑已被上面的新逻辑替代
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex.ToString());
                await Task.Delay(0, cancellationToken);
            }
        }
    }
}
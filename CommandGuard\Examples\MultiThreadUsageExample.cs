using CommandGuard.Enums;
using CommandGuard.Interfaces;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Examples;

/// <summary>
/// 多线程使用IssueTimeService的示例代码
/// 演示如何在多线程环境下安全地访问缓存的期号信息
/// </summary>
public class MultiThreadUsageExample
{
    private readonly IIssueTimeService _issueTimeService;
    private readonly ILogger<MultiThreadUsageExample> _logger;

    public MultiThreadUsageExample(IIssueTimeService issueTimeService, ILogger<MultiThreadUsageExample> logger)
    {
        _issueTimeService = issueTimeService;
        _logger = logger;
    }

    /// <summary>
    /// 示例1：多线程安全读取当前期号信息
    /// 适用于高频率查询场景，性能优秀
    /// </summary>
    public async Task Example1_SafeReadCurrentIssue()
    {
        // 启动多个并发任务读取当前期号
        var tasks = new List<Task>();

        for (int i = 0; i < 10; i++)
        {
            int taskId = i;
            tasks.Add(Task.Run(() =>
            {
                for (int j = 0; j < 100; j++)
                {
                    // 线程安全地获取当前期号信息
                    var currentIssue = _issueTimeService.GetCurrentCachedIssueTime();

                    if (currentIssue != null)
                    {
                        _logger.LogDebug($"任务{taskId}-{j}: 当前期号 {currentIssue.Issue}, " +
                                         $"开放时间 {currentIssue.OpenTime:HH:mm:ss}, " +
                                         $"关闭时间 {currentIssue.CloseTime:HH:mm:ss}");

                        // 可以安全地读取属性，但不要修改对象
                        var timeRemaining = currentIssue.CloseTime - DateTime.Now;
                        if (timeRemaining.TotalSeconds > 0)
                        {
                            _logger.LogDebug($"任务{taskId}-{j}: 距离关闭还有 {timeRemaining.TotalSeconds:F0} 秒");
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"任务{taskId}-{j}: 缓存未初始化");
                    }

                    // 模拟一些处理时间
                    Thread.Sleep(10);
                }
            }));
        }

        await Task.WhenAll(tasks);
        _logger.LogInformation("示例1完成：多线程安全读取测试");
    }

    /// <summary>
    /// 示例2：获取期号信息副本进行计算
    /// 适用于需要修改数据进行计算的场景
    /// </summary>
    public async Task Example2_GetCopyForCalculation()
    {
        var tasks = new List<Task>();

        for (int i = 0; i < 5; i++)
        {
            int taskId = i;
            tasks.Add(Task.Run(() =>
            {
                // 获取期号信息的副本
                var issueCopy = _issueTimeService.GetCurrentCachedIssueTimeCopy();

                if (issueCopy != null)
                {
                    _logger.LogInformation($"任务{taskId}: 获取到期号副本 {issueCopy.Issue}");

                    // 可以安全地修改副本，不会影响原始缓存
                    // 例如：计算调整后的时间
                    var adjustedOpenTime = issueCopy.OpenTime.AddMinutes(-1);
                    var adjustedCloseTime = issueCopy.CloseTime.AddMinutes(1);

                    _logger.LogInformation($"任务{taskId}: 调整后时间段 {adjustedOpenTime:HH:mm:ss} - {adjustedCloseTime:HH:mm:ss}");

                    // 进行一些复杂计算...
                    Thread.Sleep(100);
                }
            }));
        }

        await Task.WhenAll(tasks);
        _logger.LogInformation("示例2完成：副本计算测试");
    }

    /// <summary>
    /// 示例3：检查缓存状态
    /// 在使用缓存前先检查是否可用
    /// </summary>
    public void Example3_CheckCacheStatus()
    {
        // 检查缓存是否已初始化
        if (_issueTimeService.IsCacheInitialized())
        {
            var currentIssue = _issueTimeService.GetCurrentCachedIssueTime();
            _logger.LogInformation($"缓存可用，当前期号: {currentIssue?.Issue}");
        }
        else
        {
            _logger.LogWarning("缓存未初始化，请等待系统启动完成");
        }
    }

    /// <summary>
    /// 示例4：使用状态字段进行业务判断
    /// 展示如何使用新增的状态功能进行快速业务判断
    /// </summary>
    public void Example4_UseStatusForBusinessLogic()
    {
        // 方法1：直接检查是否可以投注（推荐）
        bool canBet = _issueTimeService.CanBetNow();
        _logger.LogInformation($"当前是否可以投注: {canBet}");

        // 方法2：获取详细状态信息
        var status = _issueTimeService.GetCurrentIssueStatus();
        _logger.LogInformation($"当前期号状态: {status.GetDisplayName()}");

        // 方法3：一次性获取期号信息和状态
        var (issueTime, issueStatus) = _issueTimeService.GetCurrentIssueTimeAndStatus();

        if (issueTime != null)
        {
            _logger.LogInformation($"期号: {issueTime.Issue}, 状态: {issueStatus.GetDisplayName()}");

            // 根据状态执行不同的业务逻辑
            switch (issueStatus)
            {
                case IssueStatus.WaitingToOpen:
                    _logger.LogInformation("投注尚未开放，可以进行预热准备");
                    break;

                case IssueStatus.PreOpening:
                    _logger.LogInformation("即将开放投注，播放提示音效");
                    // 这里可以调用音效服务播放提示音
                    break;

                case IssueStatus.Open:
                    _logger.LogInformation("投注开放中，接受用户投注");
                    break;

                case IssueStatus.PreClosing:
                    _logger.LogInformation("即将关闭投注，提醒用户抓紧时间");
                    // 这里可以调用音效服务播放警告音
                    break;

                case IssueStatus.Closed:
                    _logger.LogInformation("投注已关闭，等待开奖");
                    break;

                case IssueStatus.Unknown:
                    _logger.LogWarning("状态未知，系统可能正在初始化");
                    break;

                case IssueStatus.Maintenance:
                    _logger.LogWarning("系统维护中，暂停所有业务");
                    break;
            }
        }
    }

    /// <summary>
    /// 示例5：状态变化监控
    /// 展示如何监控状态变化并执行相应操作
    /// </summary>
    public async Task Example5_MonitorStatusChanges(CancellationToken cancellationToken)
    {
        _logger.LogInformation("开始监控期号状态变化");

        IssueStatus lastStatus = IssueStatus.Unknown;

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var currentStatus = _issueTimeService.GetCurrentIssueStatus();

                // 检查状态是否发生变化
                if (currentStatus != lastStatus)
                {
                    _logger.LogInformation($"检测到状态变化: {lastStatus.GetDisplayName()} -> {currentStatus.GetDisplayName()}");

                    // 根据新状态执行相应操作
                    await HandleStatusChange(lastStatus, currentStatus);

                    lastStatus = currentStatus;
                }

                // 每秒检查一次
                await Task.Delay(1000, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "监控状态变化时发生异常");
                await Task.Delay(5000, cancellationToken);
            }
        }

        _logger.LogInformation("状态变化监控已停止");
    }

    /// <summary>
    /// 处理状态变化
    /// </summary>
    private async Task HandleStatusChange(IssueStatus oldStatus, IssueStatus newStatus)
    {
        // 状态转换到PreOpening时播放提示音
        if (newStatus == IssueStatus.PreOpening)
        {
            _logger.LogInformation("即将开放投注，播放提示音效");
            // await _soundService.PlaySystemSoundAsync();
        }

        // 状态转换到PreClosing时播放警告音
        if (newStatus == IssueStatus.PreClosing)
        {
            _logger.LogInformation("即将关闭投注，播放警告音效");
            // await _soundService.PlayWarningSoundAsync();
        }

        // 状态转换到Open时的处理
        if (newStatus == IssueStatus.Open)
        {
            _logger.LogInformation("投注已开放，启用投注功能");
            // 启用投注按钮、开始接受投注等
        }

        // 状态转换到Closed时的处理
        if (newStatus == IssueStatus.Closed)
        {
            _logger.LogInformation("投注已关闭，禁用投注功能");
            // 禁用投注按钮、停止接受投注等
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 示例6：UI界面状态更新
    /// 展示如何在UI界面中使用状态信息
    /// </summary>
    public void Example6_UpdateUIStatus()
    {
        var (issueTime, status) = _issueTimeService.GetCurrentIssueTimeAndStatus();

        if (issueTime != null)
        {
            // 更新期号显示
            var issueText = $"当前期号: {issueTime.Issue}";
            _logger.LogInformation($"UI更新 - {issueText}");

            // 更新状态显示
            var statusText = status.GetDisplayName();
            var statusColor = status.GetStatusColor();
            _logger.LogInformation($"UI更新 - 状态: {statusText} (颜色: {statusColor})");

            // 更新倒计时显示
            if (status.ShouldShowCountdown())
            {
                var now = DateTime.Now;
                TimeSpan countdown;
                string countdownLabel;

                if (status == IssueStatus.WaitingToOpen || status == IssueStatus.PreOpening)
                {
                    countdown = issueTime.OpenTime - now;
                    countdownLabel = "距离开放";
                }
                else
                {
                    countdown = issueTime.CloseTime - now;
                    countdownLabel = "距离关闭";
                }

                if (countdown.TotalSeconds > 0)
                {
                    var countdownText = $"{countdownLabel}: {countdown:mm\\:ss}";
                    _logger.LogInformation($"UI更新 - {countdownText}");
                }
            }

            // 更新投注按钮状态
            var canBet = status.CanBet();
            _logger.LogInformation($"UI更新 - 投注按钮: {(canBet ? "启用" : "禁用")}");

            // 播放音效提醒
            if (status.ShouldPlaySound())
            {
                _logger.LogInformation($"UI更新 - 播放音效提醒: {statusText}");
            }
        }
        else
        {
            _logger.LogWarning("UI更新 - 无法获取期号信息");
        }
    }

    /// <summary>
    /// 示例7：投注前的状态验证
    /// 展示在投注前如何进行完整的状态检查
    /// </summary>
    public bool Example7_ValidateBeforeBetting(string userAccount, decimal betAmount)
    {
        _logger.LogInformation($"用户 {userAccount} 尝试投注 {betAmount} 元");

        // 第一步：检查缓存是否初始化
        if (!_issueTimeService.IsCacheInitialized())
        {
            _logger.LogWarning("投注失败：系统正在初始化中");
            return false;
        }

        // 第二步：快速检查是否允许投注
        if (!_issueTimeService.CanBetNow())
        {
            var status = _issueTimeService.GetCurrentIssueStatus();
            _logger.LogWarning($"投注失败：当前状态不允许投注 ({status.GetDisplayName()})");
            return false;
        }

        // 第三步：获取详细信息进行进一步验证
        var (issueTime, issueStatus) = _issueTimeService.GetCurrentIssueTimeAndStatus();

        if (issueTime == null)
        {
            _logger.LogWarning("投注失败：无法获取期号信息");
            return false;
        }

        // 第四步：检查剩余时间
        var now = DateTime.Now;
        var remainingSeconds = (issueTime.CloseTime - now).TotalSeconds;

        if (remainingSeconds < 5) // 关闭前5秒不允许投注
        {
            _logger.LogWarning($"投注失败：距离关闭时间过近 ({remainingSeconds:F0}秒)");
            return false;
        }

        // 第五步：根据状态给出不同的处理
        switch (issueStatus)
        {
            case IssueStatus.Open:
                _logger.LogInformation($"投注验证通过：期号 {issueTime.Issue}，剩余 {remainingSeconds:F0} 秒");
                return true;

            case IssueStatus.PreClosing:
                _logger.LogWarning($"投注警告：即将关闭，剩余 {remainingSeconds:F0} 秒");
                return true; // 仍然允许投注，但给出警告

            default:
                _logger.LogWarning($"投注失败：状态不允许投注 ({issueStatus.GetDisplayName()})");
                return false;
        }
    }

    /// <summary>
    /// 示例8：状态统计和监控
    /// 展示如何收集状态统计信息
    /// </summary>
    public async Task Example8_StatusStatistics(CancellationToken cancellationToken)
    {
        var statusCounts = new Dictionary<IssueStatus, int>();
        var statusDurations = new Dictionary<IssueStatus, TimeSpan>();
        var lastStatusTime = DateTime.Now;
        var lastStatus = _issueTimeService.GetCurrentIssueStatus();

        _logger.LogInformation("开始收集状态统计信息");

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var currentStatus = _issueTimeService.GetCurrentIssueStatus();
                var currentTime = DateTime.Now;

                // 如果状态发生变化，记录统计信息
                if (currentStatus != lastStatus)
                {
                    // 记录上一个状态的持续时间
                    var duration = currentTime - lastStatusTime;
                    if (statusDurations.ContainsKey(lastStatus))
                    {
                        statusDurations[lastStatus] += duration;
                    }
                    else
                    {
                        statusDurations[lastStatus] = duration;
                    }

                    // 记录状态切换次数
                    statusCounts[currentStatus] = statusCounts.GetValueOrDefault(currentStatus, 0) + 1;

                    _logger.LogInformation($"状态统计 - {lastStatus.GetDisplayName()} 持续了 {duration.TotalSeconds:F0} 秒");

                    lastStatus = currentStatus;
                    lastStatusTime = currentTime;
                }

                await Task.Delay(1000, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "收集状态统计信息时发生异常");
                await Task.Delay(5000, cancellationToken);
            }
        }

        // 输出统计结果
        _logger.LogInformation("状态统计结果:");
        foreach (var kvp in statusCounts)
        {
            var avgDuration = statusDurations.ContainsKey(kvp.Key)
                ? statusDurations[kvp.Key].TotalSeconds / kvp.Value
                : 0;
            _logger.LogInformation($"  {kvp.Key.GetDisplayName()}: 出现 {kvp.Value} 次，平均持续 {avgDuration:F0} 秒");
        }
    }
}
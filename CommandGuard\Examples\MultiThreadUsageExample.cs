using CommandGuard.Interfaces;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Examples;

/// <summary>
/// 多线程使用IssueTimeService的示例代码
/// 演示如何在多线程环境下安全地访问缓存的期号信息
/// </summary>
public class MultiThreadUsageExample
{
    private readonly IIssueTimeService _issueTimeService;
    private readonly ILogger<MultiThreadUsageExample> _logger;

    public MultiThreadUsageExample(IIssueTimeService issueTimeService, ILogger<MultiThreadUsageExample> logger)
    {
        _issueTimeService = issueTimeService;
        _logger = logger;
    }

    /// <summary>
    /// 示例1：多线程安全读取当前期号信息
    /// 适用于高频率查询场景，性能优秀
    /// </summary>
    public async Task Example1_SafeReadCurrentIssue()
    {
        // 启动多个并发任务读取当前期号
        var tasks = new List<Task>();

        for (int i = 0; i < 10; i++)
        {
            int taskId = i;
            tasks.Add(Task.Run(() =>
            {
                for (int j = 0; j < 100; j++)
                {
                    // 线程安全地获取当前期号信息
                    var currentIssue = _issueTimeService.GetCurrentCachedIssueTime();

                    if (currentIssue != null)
                    {
                        _logger.LogDebug($"任务{taskId}-{j}: 当前期号 {currentIssue.Issue}, " +
                                         $"开放时间 {currentIssue.OpenTime:HH:mm:ss}, " +
                                         $"关闭时间 {currentIssue.CloseTime:HH:mm:ss}");

                        // 可以安全地读取属性，但不要修改对象
                        var timeRemaining = currentIssue.CloseTime - DateTime.Now;
                        if (timeRemaining.TotalSeconds > 0)
                        {
                            _logger.LogDebug($"任务{taskId}-{j}: 距离关闭还有 {timeRemaining.TotalSeconds:F0} 秒");
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"任务{taskId}-{j}: 缓存未初始化");
                    }

                    // 模拟一些处理时间
                    Thread.Sleep(10);
                }
            }));
        }

        await Task.WhenAll(tasks);
        _logger.LogInformation("示例1完成：多线程安全读取测试");
    }

    /// <summary>
    /// 示例2：获取期号信息副本进行计算
    /// 适用于需要修改数据进行计算的场景
    /// </summary>
    public async Task Example2_GetCopyForCalculation()
    {
        var tasks = new List<Task>();

        for (int i = 0; i < 5; i++)
        {
            int taskId = i;
            tasks.Add(Task.Run(() =>
            {
                // 获取期号信息的副本
                var issueCopy = _issueTimeService.GetCurrentCachedIssueTimeCopy();

                if (issueCopy != null)
                {
                    _logger.LogInformation($"任务{taskId}: 获取到期号副本 {issueCopy.Issue}");

                    // 可以安全地修改副本，不会影响原始缓存
                    // 例如：计算调整后的时间
                    var adjustedOpenTime = issueCopy.OpenTime.AddMinutes(-1);
                    var adjustedCloseTime = issueCopy.CloseTime.AddMinutes(1);

                    _logger.LogInformation($"任务{taskId}: 调整后时间段 {adjustedOpenTime:HH:mm:ss} - {adjustedCloseTime:HH:mm:ss}");

                    // 进行一些复杂计算...
                    Thread.Sleep(100);
                }
            }));
        }

        await Task.WhenAll(tasks);
        _logger.LogInformation("示例2完成：副本计算测试");
    }

    /// <summary>
    /// 示例3：检查缓存状态
    /// 在使用缓存前先检查是否可用
    /// </summary>
    public void Example3_CheckCacheStatus()
    {
        // 检查缓存是否已初始化
        if (_issueTimeService.IsCacheInitialized())
        {
            var currentIssue = _issueTimeService.GetCurrentCachedIssueTime();
            _logger.LogInformation($"缓存可用，当前期号: {currentIssue?.Issue}");
        }
        else
        {
            _logger.LogWarning("缓存未初始化，请等待系统启动完成");
        }
    }

    /// <summary>
    /// 示例4：业务逻辑中的实际应用
    /// 模拟投注系统中检查期号状态的场景
    /// </summary>
    public bool Example4_CheckBettingAvailable()
    {
        // 获取当前期号信息
        var currentIssue = _issueTimeService.GetCurrentCachedIssueTime();

        if (currentIssue == null)
        {
            _logger.LogWarning("无法获取当前期号信息，投注不可用");
            return false;
        }

        var now = DateTime.Now;

        // 检查是否在投注时间内
        if (now >= currentIssue.OpenTime && now <= currentIssue.CloseTime)
        {
            var remainingSeconds = (currentIssue.CloseTime - now).TotalSeconds;
            _logger.LogInformation($"期号 {currentIssue.Issue} 投注可用，剩余 {remainingSeconds:F0} 秒");
            return true;
        }
        else if (now < currentIssue.OpenTime)
        {
            var waitSeconds = (currentIssue.OpenTime - now).TotalSeconds;
            _logger.LogInformation($"期号 {currentIssue.Issue} 尚未开放，需等待 {waitSeconds:F0} 秒");
            return false;
        }
        else
        {
            _logger.LogInformation($"期号 {currentIssue.Issue} 已关闭，投注不可用");
            return false;
        }
    }

    /// <summary>
    /// 示例5：定时任务中的使用
    /// 模拟定时检查期号状态的后台任务
    /// </summary>
    public async Task Example5_PeriodicCheck(CancellationToken cancellationToken)
    {
        _logger.LogInformation("开始定时检查期号状态");

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var currentIssue = _issueTimeService.GetCurrentCachedIssueTime();

                if (currentIssue != null)
                {
                    var now = DateTime.Now;
                    var status = GetIssueStatus(currentIssue, now);

                    _logger.LogDebug($"期号状态检查 - {currentIssue.Issue}: {status}");

                    // 根据状态执行相应的业务逻辑
                    switch (status)
                    {
                        case "即将开放":
                            // 准备开放投注的逻辑
                            break;
                        case "投注中":
                            // 投注进行中的逻辑
                            break;
                        case "即将关闭":
                            // 准备关闭投注的逻辑
                            break;
                        case "已关闭":
                            // 投注已关闭的逻辑
                            break;
                    }
                }

                // 每秒检查一次
                await Task.Delay(1000, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定时检查期号状态时发生异常");
                await Task.Delay(5000, cancellationToken); // 异常时等待5秒再重试
            }
        }

        _logger.LogInformation("定时检查期号状态已停止");
    }

    /// <summary>
    /// 获取期号状态描述
    /// </summary>
    private string GetIssueStatus(IssueTime issue, DateTime now)
    {
        if (now < issue.OpenTime)
        {
            var timeToOpen = issue.OpenTime - now;
            return timeToOpen.TotalMinutes <= 1 ? "即将开放" : "等待开放";
        }
        else if (now <= issue.CloseTime)
        {
            var timeToClose = issue.CloseTime - now;
            return timeToClose.TotalMinutes <= 1 ? "即将关闭" : "投注中";
        }
        else
        {
            return "已关闭";
        }
    }
}
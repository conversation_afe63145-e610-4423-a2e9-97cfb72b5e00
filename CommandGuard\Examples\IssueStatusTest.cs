using CommandGuard.Enums;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Examples;

/// <summary>
/// 期号状态功能测试类
/// 用于验证IssueStatus枚举和扩展方法的功能
/// </summary>
public class IssueStatusTest
{
    private readonly ILogger<IssueStatusTest> _logger;

    public IssueStatusTest(ILogger<IssueStatusTest> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 测试所有状态的基本功能
    /// </summary>
    public void TestAllStatusBasicFunctions()
    {
        _logger.LogInformation("开始测试期号状态基本功能");

        var allStatuses = Enum.GetValues<IssueStatus>();

        foreach (var status in allStatuses)
        {
            _logger.LogInformation($"测试状态: {status}");
            _logger.LogInformation($"  显示名称: {status.GetDisplayName()}");
            _logger.LogInformation($"  颜色代码: {status.GetStatusColor()}");
            _logger.LogInformation($"  可以投注: {status.CanBet()}");
            _logger.LogInformation($"  是否活跃: {status.IsActive()}");
            _logger.LogInformation($"  显示倒计时: {status.ShouldShowCountdown()}");
            _logger.LogInformation($"  播放音效: {status.ShouldPlaySound()}");
            _logger.LogInformation("");
        }
    }

    /// <summary>
    /// 测试投注相关的状态判断
    /// </summary>
    public void TestBettingLogic()
    {
        _logger.LogInformation("测试投注相关逻辑");

        // 测试允许投注的状态
        var bettingAllowedStatuses = new[] { IssueStatus.Open, IssueStatus.PreClosing };
        foreach (var status in bettingAllowedStatuses)
        {
            if (status.CanBet())
            {
                _logger.LogInformation($"✓ {status.GetDisplayName()} 状态正确允许投注");
            }
            else
            {
                _logger.LogError($"✗ {status.GetDisplayName()} 状态应该允许投注但返回false");
            }
        }

        // 测试不允许投注的状态
        var bettingNotAllowedStatuses = new[] 
        { 
            IssueStatus.Unknown, 
            IssueStatus.WaitingToOpen, 
            IssueStatus.PreOpening, 
            IssueStatus.Closed, 
            IssueStatus.Maintenance 
        };
        
        foreach (var status in bettingNotAllowedStatuses)
        {
            if (!status.CanBet())
            {
                _logger.LogInformation($"✓ {status.GetDisplayName()} 状态正确禁止投注");
            }
            else
            {
                _logger.LogError($"✗ {status.GetDisplayName()} 状态应该禁止投注但返回true");
            }
        }
    }

    /// <summary>
    /// 测试UI相关的状态功能
    /// </summary>
    public void TestUIRelatedFunctions()
    {
        _logger.LogInformation("测试UI相关功能");

        // 测试需要显示倒计时的状态
        var countdownStatuses = new[] 
        { 
            IssueStatus.WaitingToOpen, 
            IssueStatus.PreOpening, 
            IssueStatus.Open, 
            IssueStatus.PreClosing 
        };

        foreach (var status in countdownStatuses)
        {
            if (status.ShouldShowCountdown())
            {
                _logger.LogInformation($"✓ {status.GetDisplayName()} 正确显示倒计时");
            }
            else
            {
                _logger.LogError($"✗ {status.GetDisplayName()} 应该显示倒计时");
            }
        }

        // 测试需要播放音效的状态
        var soundStatuses = new[] { IssueStatus.PreOpening, IssueStatus.PreClosing };
        foreach (var status in soundStatuses)
        {
            if (status.ShouldPlaySound())
            {
                _logger.LogInformation($"✓ {status.GetDisplayName()} 正确播放音效");
            }
            else
            {
                _logger.LogError($"✗ {status.GetDisplayName()} 应该播放音效");
            }
        }

        // 测试颜色代码
        var expectedColors = new Dictionary<IssueStatus, string>
        {
            { IssueStatus.Unknown, "#808080" },
            { IssueStatus.WaitingToOpen, "#FFA500" },
            { IssueStatus.PreOpening, "#FFD700" },
            { IssueStatus.Open, "#00FF00" },
            { IssueStatus.PreClosing, "#FF4500" },
            { IssueStatus.Closed, "#FF0000" },
            { IssueStatus.Maintenance, "#800080" }
        };

        foreach (var kvp in expectedColors)
        {
            var actualColor = kvp.Key.GetStatusColor();
            if (actualColor == kvp.Value)
            {
                _logger.LogInformation($"✓ {kvp.Key.GetDisplayName()} 颜色正确: {actualColor}");
            }
            else
            {
                _logger.LogError($"✗ {kvp.Key.GetDisplayName()} 颜色错误: 期望 {kvp.Value}, 实际 {actualColor}");
            }
        }
    }

    /// <summary>
    /// 测试状态转换逻辑
    /// </summary>
    public void TestStatusTransitions()
    {
        _logger.LogInformation("测试状态转换逻辑");

        // 模拟一个完整的期号生命周期
        var lifecycle = new[]
        {
            IssueStatus.WaitingToOpen,
            IssueStatus.PreOpening,
            IssueStatus.Open,
            IssueStatus.PreClosing,
            IssueStatus.Closed
        };

        _logger.LogInformation("模拟期号生命周期:");
        for (int i = 0; i < lifecycle.Length; i++)
        {
            var currentStatus = lifecycle[i];
            var nextStatus = i < lifecycle.Length - 1 ? lifecycle[i + 1] : (IssueStatus?)null;

            _logger.LogInformation($"  阶段 {i + 1}: {currentStatus.GetDisplayName()}");
            _logger.LogInformation($"    可投注: {currentStatus.CanBet()}");
            _logger.LogInformation($"    显示倒计时: {currentStatus.ShouldShowCountdown()}");
            _logger.LogInformation($"    播放音效: {currentStatus.ShouldPlaySound()}");

            if (nextStatus.HasValue)
            {
                _logger.LogInformation($"    下一状态: {nextStatus.Value.GetDisplayName()}");
            }
        }
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public void RunAllTests()
    {
        _logger.LogInformation("=== 开始期号状态功能测试 ===");

        try
        {
            TestAllStatusBasicFunctions();
            TestBettingLogic();
            TestUIRelatedFunctions();
            TestStatusTransitions();

            _logger.LogInformation("=== 所有测试完成 ===");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试过程中发生异常");
        }
    }

    /// <summary>
    /// 性能测试：测试状态判断方法的性能
    /// </summary>
    public void PerformanceTest()
    {
        _logger.LogInformation("开始性能测试");

        const int iterations = 1000000;
        var status = IssueStatus.Open;

        // 测试CanBet方法性能
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        for (int i = 0; i < iterations; i++)
        {
            _ = status.CanBet();
        }
        stopwatch.Stop();

        _logger.LogInformation($"CanBet方法性能测试: {iterations} 次调用耗时 {stopwatch.ElapsedMilliseconds} 毫秒");
        _logger.LogInformation($"平均每次调用: {(double)stopwatch.ElapsedTicks / iterations} ticks");

        // 测试GetDisplayName方法性能
        stopwatch.Restart();
        for (int i = 0; i < iterations; i++)
        {
            _ = status.GetDisplayName();
        }
        stopwatch.Stop();

        _logger.LogInformation($"GetDisplayName方法性能测试: {iterations} 次调用耗时 {stopwatch.ElapsedMilliseconds} 毫秒");
        _logger.LogInformation($"平均每次调用: {(double)stopwatch.ElapsedTicks / iterations} ticks");
    }
}

namespace CommandGuard.Enums;

/// <summary>
/// 期号状态枚举
/// 定义期号在不同时间阶段的状态，用于业务逻辑判断和状态管理
/// </summary>
public enum IssueStatus
{
    /// <summary>
    /// 未知状态
    /// 
    /// 使用场景：
    /// - 系统刚启动，缓存未初始化时
    /// - 发生异常导致状态无法确定时
    /// - 作为默认初始状态
    /// 
    /// 业务处理：
    /// - 应该等待状态更新或重新获取数据
    /// - 不应该进行任何业务操作
    /// - 可以显示"系统初始化中"等提示
    /// </summary>
    Unknown = 0,

    /// <summary>
    /// 等待开放状态
    /// 
    /// 使用场景：
    /// - 当前时间早于期号开放时间
    /// - 距离开放时间还有较长时间（通常超过1分钟）
    /// 
    /// 业务处理：
    /// - 投注功能不可用
    /// - 可以显示倒计时到开放时间
    /// - 可以进行预热准备工作
    /// 
    /// 状态转换：
    /// - 随着时间推进，会转换为 PreOpening 或 Open
    /// </summary>
    WaitingToOpen = 1,

    /// <summary>
    /// 即将开放状态
    /// 
    /// 使用场景：
    /// - 距离开放时间很近（通常1分钟内）
    /// - 需要进行开放前的最后准备
    /// 
    /// 业务处理：
    /// - 投注功能仍不可用，但可以显示"即将开放"
    /// - 可以播放提示音效
    /// - 进行系统预热和准备工作
    /// - 显示精确的倒计时
    /// 
    /// 状态转换：
    /// - 时间到达后转换为 Open
    /// </summary>
    PreOpening = 2,

    /// <summary>
    /// 开放状态（投注中）
    /// 
    /// 使用场景：
    /// - 当前时间在期号的开放时间和关闭时间之间
    /// - 距离关闭时间还有较长时间（通常超过1分钟）
    /// 
    /// 业务处理：
    /// - 投注功能完全可用
    /// - 可以接受用户投注
    /// - 显示剩余投注时间
    /// - 处理各种投注业务逻辑
    /// 
    /// 状态转换：
    /// - 随着时间推进，会转换为 PreClosing
    /// </summary>
    Open = 3,

    /// <summary>
    /// 即将关闭状态
    /// 
    /// 使用场景：
    /// - 距离关闭时间很近（通常1分钟内）
    /// - 需要提醒用户抓紧时间投注
    /// 
    /// 业务处理：
    /// - 投注功能仍然可用
    /// - 显示紧急倒计时提醒
    /// - 可以播放警告音效
    /// - 可能限制某些复杂投注操作
    /// 
    /// 状态转换：
    /// - 时间到达后转换为 Closed
    /// </summary>
    PreClosing = 4,

    /// <summary>
    /// 关闭状态
    /// 
    /// 使用场景：
    /// - 当前时间超过期号关闭时间
    /// - 等待开奖或下一期开放
    /// 
    /// 业务处理：
    /// - 投注功能完全不可用
    /// - 可以显示"投注已截止"
    /// - 等待开奖结果
    /// - 准备下一期的相关工作
    /// 
    /// 状态转换：
    /// - 当下一期数据可用时，转换为对应的新状态
    /// </summary>
    Closed = 5,

    /// <summary>
    /// 系统维护状态
    /// 
    /// 使用场景：
    /// - 系统需要维护或升级
    /// - 发生严重错误需要暂停服务
    /// - 管理员手动设置的维护状态
    /// 
    /// 业务处理：
    /// - 所有业务功能不可用
    /// - 显示维护提示信息
    /// - 记录维护日志
    /// 
    /// 状态转换：
    /// - 维护完成后，根据当前时间转换为对应状态
    /// </summary>
    Maintenance = 6
}

/// <summary>
/// 期号状态扩展方法
/// 提供状态相关的便捷操作和判断方法
/// </summary>
public static class IssueStatusExtensions
{
    /// <summary>
    /// 判断当前状态是否允许投注
    /// </summary>
    /// <param name="status">期号状态</param>
    /// <returns>如果允许投注返回true，否则返回false</returns>
    public static bool CanBet(this IssueStatus status)
    {
        return status == IssueStatus.Open || status == IssueStatus.PreClosing;
    }

    /// <summary>
    /// 判断当前状态是否为活跃状态（非关闭、非维护）
    /// </summary>
    /// <param name="status">期号状态</param>
    /// <returns>如果为活跃状态返回true，否则返回false</returns>
    public static bool IsActive(this IssueStatus status)
    {
        return status != IssueStatus.Closed && 
               status != IssueStatus.Maintenance && 
               status != IssueStatus.Unknown;
    }

    /// <summary>
    /// 判断当前状态是否需要显示倒计时
    /// </summary>
    /// <param name="status">期号状态</param>
    /// <returns>如果需要显示倒计时返回true，否则返回false</returns>
    public static bool ShouldShowCountdown(this IssueStatus status)
    {
        return status == IssueStatus.WaitingToOpen || 
               status == IssueStatus.PreOpening || 
               status == IssueStatus.Open || 
               status == IssueStatus.PreClosing;
    }

    /// <summary>
    /// 获取状态的中文描述
    /// </summary>
    /// <param name="status">期号状态</param>
    /// <returns>状态的中文描述字符串</returns>
    public static string GetDisplayName(this IssueStatus status)
    {
        return status switch
        {
            IssueStatus.Unknown => "未知状态",
            IssueStatus.WaitingToOpen => "等待开放",
            IssueStatus.PreOpening => "即将开放",
            IssueStatus.Open => "投注中",
            IssueStatus.PreClosing => "即将截止",
            IssueStatus.Closed => "已截止",
            IssueStatus.Maintenance => "系统维护",
            _ => "未知状态"
        };
    }

    /// <summary>
    /// 获取状态对应的颜色代码（用于UI显示）
    /// </summary>
    /// <param name="status">期号状态</param>
    /// <returns>颜色代码字符串</returns>
    public static string GetStatusColor(this IssueStatus status)
    {
        return status switch
        {
            IssueStatus.Unknown => "#808080",        // 灰色
            IssueStatus.WaitingToOpen => "#FFA500",  // 橙色
            IssueStatus.PreOpening => "#FFD700",     // 金色
            IssueStatus.Open => "#00FF00",           // 绿色
            IssueStatus.PreClosing => "#FF4500",     // 红橙色
            IssueStatus.Closed => "#FF0000",         // 红色
            IssueStatus.Maintenance => "#800080",    // 紫色
            _ => "#808080"                            // 默认灰色
        };
    }

    /// <summary>
    /// 判断是否需要播放音效提醒
    /// </summary>
    /// <param name="status">期号状态</param>
    /// <returns>如果需要播放音效返回true，否则返回false</returns>
    public static bool ShouldPlaySound(this IssueStatus status)
    {
        return status == IssueStatus.PreOpening || status == IssueStatus.PreClosing;
    }
}

﻿using CommandGuard.Interfaces;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

/// <summary>
/// 时间发放服务实现类
/// 负责处理发放时间的业务逻辑，包括数据的创建、查询、删除等操作
/// </summary>
public class IssueTimeService : IIssueTimeService
{
    #region 常量定义

    /// <summary>
    /// 每天发放次数
    /// </summary>
    private const int DailyIssueCount = 203;

    /// <summary>
    /// 发放间隔时间（分钟）
    /// </summary>
    private const int IntervalMinutes = 5;

    /// <summary>
    /// 每次发放持续时间（分钟）
    /// </summary>
    private const int DurationMinutes = 5;

    /// <summary>
    /// 提前关闭时间（秒）
    /// </summary>
    private const int EarlyCloseSeconds = 10;

    /// <summary>
    /// 每天开始发放的小时
    /// </summary>
    private const int StartHour = 7;

    /// <summary>
    /// 缓存有效时间阈值（秒）
    /// 在即将开放前的这个时间内，缓存失效以确保及时更新状态
    /// </summary>
    private const double CacheValidThresholdSeconds = 30.0;

    /// <summary>
    /// 民国元年（1912年）
    /// </summary>
    private const int RepublicOfChinaFirstYear = 1912;

    #endregion

    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<IssueTimeService> _logger;

    /// <summary>
    /// FreeSql ORM 实例，用于数据库操作
    /// </summary>
    private readonly IDbService _dbService;

    /// <summary>
    /// 缓存的发放时间对象，避免频繁查询数据库
    /// </summary>
    private IssueTime? _cachedCurrentIssueTime;

    /// <summary>
    /// 缓存读写锁，优化多线程读取性能
    /// 读多写少场景下，允许多个线程并发读取，提高性能
    /// </summary>
    private readonly ReaderWriterLockSlim _cacheRwLock = new();

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数，通过依赖注入获取 dbService 实例
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="dbService">dbService 实例</param>
    public IssueTimeService(ILogger<IssueTimeService> logger, IDbService dbService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
    }

    #endregion

    #region 公共缓存访问方法

    /// <summary>
    /// 线程安全地获取当前缓存的发放时间信息
    ///
    /// 功能：为多线程环境提供安全的缓存读取接口
    ///
    /// 性能优化：
    /// - 使用读写锁，允许多个线程并发读取
    /// - 读取操作不会阻塞其他读取操作
    /// - 只有在写入时才会阻塞读取
    ///
    /// 线程安全：
    /// - 使用ReaderWriterLockSlim确保读取安全
    /// - 防止在读取过程中缓存被修改
    /// - 返回的是引用，调用方不应修改返回的对象
    ///
    /// 使用场景：
    /// - 多个业务模块需要获取当前期号信息
    /// - 高频率的期号状态查询
    /// - 实时显示当前期号和时间信息
    ///
    /// 注意事项：
    /// - 返回null表示缓存未初始化或已清除
    /// - 返回的对象不应被修改，以保持缓存一致性
    /// - 调用方应该检查返回值是否为null
    /// </summary>
    /// <returns>当前缓存的发放时间对象，如果缓存为空则返回null</returns>
    public IssueTime? GetCurrentCachedIssueTime()
    {
        _cacheRwLock.EnterReadLock();
        try
        {
            return _cachedCurrentIssueTime;
        }
        finally
        {
            _cacheRwLock.ExitReadLock();
        }
    }

    /// <summary>
    /// 线程安全地获取当前缓存发放时间信息的副本
    ///
    /// 功能：返回缓存对象的深度副本，调用方可以安全修改
    ///
    /// 优点：
    /// - 调用方可以安全修改返回的对象
    /// - 不会影响原始缓存数据
    /// - 适合需要修改数据的业务场景
    ///
    /// 缺点：
    /// - 性能开销较大（需要创建新对象）
    /// - 内存使用增加
    ///
    /// 使用场景：
    /// - 需要修改期号信息进行计算的场景
    /// - 需要保存历史快照的场景
    /// - 对数据安全性要求极高的场景
    /// </summary>
    /// <returns>当前缓存发放时间对象的副本，如果缓存为空则返回null</returns>
    public IssueTime? GetCurrentCachedIssueTimeCopy()
    {
        _cacheRwLock.EnterReadLock();
        try
        {
            if (_cachedCurrentIssueTime == null)
                return null;

            // 创建深度副本
            return new IssueTime
            {
                Id = _cachedCurrentIssueTime.Id,
                Issue = _cachedCurrentIssueTime.Issue,
                OpenTime = _cachedCurrentIssueTime.OpenTime,
                CloseTime = _cachedCurrentIssueTime.CloseTime
            };
        }
        finally
        {
            _cacheRwLock.ExitReadLock();
        }
    }

    /// <summary>
    /// 检查缓存是否已初始化且有效
    ///
    /// 功能：线程安全地检查缓存状态
    ///
    /// 使用场景：
    /// - 在使用缓存前检查是否可用
    /// - 业务逻辑中的条件判断
    /// - 系统状态监控
    /// </summary>
    /// <returns>如果缓存已初始化且有效返回true，否则返回false</returns>
    public bool IsCacheInitialized()
    {
        _cacheRwLock.EnterReadLock();
        try
        {
            return _cachedCurrentIssueTime != null;
        }
        finally
        {
            _cacheRwLock.ExitReadLock();
        }
    }

    #endregion

    #region 数据创建和管理方法

    /// <summary>
    /// 创建发放记录
    /// 生成一整年的发放时间数据，每天203次，每次间隔5分钟
    /// </summary>
    public async Task<IssueTime> CreateIssueTimeAsync(DateTime dateTime)
    {
        var targetYear = dateTime.Year;
        _logger.LogDebug($"开始创建 {targetYear} 年的发放时间数据");

        try
        {
            // 检查指定年份是否已有数据
            var existingFirstIssueTime = await _dbService.FreeSql.Select<IssueTime>()
                .Where(x => x.OpenTime.Year == targetYear)
                .OrderBy(x => x.OpenTime)
                .FirstAsync();

            if (existingFirstIssueTime != null)
            {
                _logger.LogDebug($"{targetYear} 年的发放时间数据已存在，返回第一条记录: {existingFirstIssueTime.Issue}");
                return existingFirstIssueTime;
            }

            // 生成全年发放时间数据
            var issueTimeList = GenerateYearlyIssueTimeData(targetYear);

            _logger.LogInformation($"生成了 {issueTimeList.Count} 条 {targetYear} 年的发放时间记录，开始批量插入数据库");

            // 批量插入数据库
            var affectedRows = await _dbService.FreeSql.Insert(issueTimeList).ExecuteAffrowsAsync();

            _logger.LogInformation($"成功插入 {affectedRows} 条 {targetYear} 年的发放时间记录");

            return issueTimeList[0];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"创建 {targetYear} 年发放时间数据时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 生成指定年份的全年发放时间数据
    /// </summary>
    /// <param name="year">目标年份</param>
    /// <returns>发放时间数据列表</returns>
    private List<IssueTime> GenerateYearlyIssueTimeData(int year)
    {
        var issueTimeList = new List<IssueTime>();
        var index = 0; // 全局序号计数器
        var mingGuoYear = year - RepublicOfChinaFirstYear + 1; // 民国年份

        _logger.LogDebug($"开始生成 {year} 年数据，民国年份: {mingGuoYear}");

        for (int month = 1; month <= 12; month++)
        {
            var daysInMonth = DateTime.DaysInMonth(year, month);

            for (int day = 1; day <= daysInMonth; day++)
            {
                // 每天从开始时间前5分钟开始
                var currentTime = new DateTime(year, month, day, StartHour, 0, 0).AddMinutes(-IntervalMinutes);

                for (int i = 0; i < DailyIssueCount; i++)
                {
                    var openTime = currentTime.AddMinutes(IntervalMinutes);
                    var closeTime = openTime.AddMinutes(DurationMinutes).AddSeconds(-EarlyCloseSeconds);
                    index++;

                    var issueNumber = GenerateIssueNumber(mingGuoYear, index);

                    issueTimeList.Add(new IssueTime
                    {
                        Issue = issueNumber,
                        OpenTime = openTime,
                        CloseTime = closeTime
                    });

                    currentTime = openTime; // 更新到下一个时间点
                }
            }
        }

        var daysInYear = DateTime.IsLeapYear(year) ? 366 : 365;
        _logger.LogDebug($"{year} 年共 {daysInYear} 天，生成 {index} 条发放记录");

        return issueTimeList;
    }

    /// <summary>
    /// 生成发放编号
    /// </summary>
    /// <param name="mingGuoYear">民国年份</param>
    /// <param name="sequenceNumber">序号</param>
    /// <returns>发放编号</returns>
    private static string GenerateIssueNumber(int mingGuoYear, int sequenceNumber)
    {
        return $"{mingGuoYear}{sequenceNumber:D6}";
    }

    #endregion

    #region 数据维护方法

    /// <summary>
    /// 获取当前正在进行的发放时间段
    /// </summary>
    /// <param name="cancellationToken"></param>
    public async Task UpdateCurrentIssueTimeAsync(CancellationToken cancellationToken)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(1000, cancellationToken);
                DateTime now = DateTime.Now;

                // 检查缓存是否有效（使用读写锁优化性能）
                _cacheRwLock.EnterReadLock();
                bool cacheValid = false;
                try
                {
                    if (_cachedCurrentIssueTime != null && IsCacheValid(_cachedCurrentIssueTime, now))
                    {
                        _logger.LogDebug($"使用缓存数据: {_cachedCurrentIssueTime.Issue}");
                        cacheValid = true;
                    }
                }
                finally
                {
                    _cacheRwLock.ExitReadLock();
                }

                if (cacheValid)
                {
                    continue;
                }

                // 缓存无效，需要重新查询数据库
                _logger.LogDebug($"缓存过期，重新查询数据库 - 当前时间: {now:yyyy-MM-dd HH:mm:ss}");
                IssueTime? resultIssueTime = null;

                // 优化：使用单个查询同时查找当前和下一个时间段
                var candidateIssues = await _dbService.FreeSql.Select<IssueTime>()
                    .Where(x => x.CloseTime >= now || x.OpenTime > now) // 包含当前正在进行的和未来的时间段
                    .OrderBy(x => x.OpenTime)
                    .Take(2) // 最多取2条：当前进行中的和下一个
                    .ToListAsync(cancellationToken);

                if (candidateIssues.Count > 0)
                {
                    // 优先选择当前正在进行的时间段
                    var currentIssue = candidateIssues.FirstOrDefault(x => x.OpenTime <= now && x.CloseTime >= now);
                    if (currentIssue != null)
                    {
                        resultIssueTime = currentIssue;
                        _logger.LogInformation($"找到当前进行中的发放时间段: {currentIssue.Issue} ({currentIssue.OpenTime:HH:mm:ss} - {currentIssue.CloseTime:HH:mm:ss})");
                    }
                    else
                    {
                        // 没有当前进行的，选择最近的未来时间段
                        var nextIssue = candidateIssues.FirstOrDefault(x => x.OpenTime > now);
                        if (nextIssue != null)
                        {
                            resultIssueTime = nextIssue;
                            _logger.LogInformation($"找到下一个发放时间段: {nextIssue.Issue} ({nextIssue.OpenTime:HH:mm:ss} - {nextIssue.CloseTime:HH:mm:ss})");
                        }
                    }
                }

                // 如果没有找到任何时间段，则创建下一年的数据
                resultIssueTime ??= await HandleNoFutureIssueTimeAsync(now);

                // 更新缓存
                if (resultIssueTime != null)
                {
                    UpdateCache(resultIssueTime);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新发放时间时发生错误");
        }
    }

    /// <summary>
    /// 处理没有找到未来时间段的情况，自动创建下一年数据
    /// </summary>
    /// <param name="currentTime">当前时间</param>
    /// <returns>新创建的第一个发放时间段</returns>
    private async Task<IssueTime?> HandleNoFutureIssueTimeAsync(DateTime currentTime)
    {
        try
        {
            // 确定下一年的年份
            var nextYear = currentTime.Year + 1;
            var nextYearDate = new DateTime(nextYear, 1, 1);

            _logger.LogInformation($"当前年份 {currentTime.Year} 没有未来的发放时间段，开始创建 {nextYear} 年的数据");

            var newFirstIssueTime = await CreateIssueTimeAsync(nextYearDate);

            _logger.LogInformation($"成功创建 {nextYear} 年的发放时间数据，第一个时间段: {newFirstIssueTime.Issue}");

            return newFirstIssueTime;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建下一年发放时间数据时发生错误");
            return null;
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 更新缓存（线程安全）
    /// 使用写锁确保更新操作的原子性和一致性
    /// </summary>
    /// <param name="issueTime">要缓存的发放时间对象</param>
    private void UpdateCache(IssueTime issueTime)
    {
        _cacheRwLock.EnterWriteLock();
        try
        {
            _cachedCurrentIssueTime = issueTime;
            _logger.LogInformation($"缓存已更新: {issueTime.Issue} ({issueTime.OpenTime:HH:mm:ss} - {issueTime.CloseTime:HH:mm:ss})");
        }
        finally
        {
            _cacheRwLock.ExitWriteLock();
        }
    }

    /// <summary>
    /// 清除缓存
    /// 强制下次查询时重新从数据库获取数据
    /// 使用写锁确保清除操作的原子性
    /// </summary>
    public void ClearCache()
    {
        _cacheRwLock.EnterWriteLock();
        try
        {
            var hadCache = _cachedCurrentIssueTime != null;
            _cachedCurrentIssueTime = null;

            if (hadCache)
            {
                _logger.LogInformation("发放时间缓存已清除");
            }
        }
        finally
        {
            _cacheRwLock.ExitWriteLock();
        }
    }

    /// <summary>
    /// 检查缓存是否仍然有效
    /// 优化后的缓存策略：最大化缓存利用率，最小化数据库查询
    /// </summary>
    /// <param name="cachedIssueTime">缓存的发放时间对象</param>
    /// <param name="currentTime">当前时间</param>
    /// <returns>如果缓存有效返回true，否则返回false</returns>
    private bool IsCacheValid(IssueTime cachedIssueTime, DateTime currentTime)
    {
        // 情况1：当前时间在发放时间段内 - 缓存绝对有效
        if (currentTime >= cachedIssueTime.OpenTime && currentTime <= cachedIssueTime.CloseTime)
        {
            _logger.LogTrace($"缓存有效：当前时间在发放时间段内 ({cachedIssueTime.OpenTime:HH:mm:ss} - {cachedIssueTime.CloseTime:HH:mm:ss})");
            return true;
        }

        // 情况2：当前时间在发放时间段之前
        if (currentTime < cachedIssueTime.OpenTime)
        {
            var timeToOpen = cachedIssueTime.OpenTime - currentTime;

            // 只有在即将开放前的很短时间内才让缓存失效
            // 这样可以确保状态及时更新，同时最大化缓存利用率
            if (timeToOpen.TotalSeconds > CacheValidThresholdSeconds)
            {
                _logger.LogTrace($"缓存有效：距离开放还有 {timeToOpen.TotalMinutes:F1} 分钟，超过阈值");
                return true;
            }
            else
            {
                _logger.LogTrace($"缓存失效：距离开放仅剩 {timeToOpen.TotalSeconds:F0} 秒，需要及时更新状态");
                return false;
            }
        }

        // 情况3：当前时间已超过关闭时间
        // 需要查询下一个时间段，但这里有个重要优化点：
        // 如果我们知道下一个时间段的开始时间，可以继续使用缓存直到接近下个时间段
        var timeSinceClose = currentTime - cachedIssueTime.CloseTime;

        // 如果刚刚关闭（在一个发放间隔内），我们可以计算下一个时间段
        if (timeSinceClose.TotalMinutes < IntervalMinutes)
        {
            // 计算下一个预期的开放时间（当前关闭时间 + 间隔时间 - 持续时间 + 10秒）
            var nextExpectedOpenTime = cachedIssueTime.CloseTime.AddSeconds(EarlyCloseSeconds).AddMinutes(IntervalMinutes - DurationMinutes);
            var timeToNextOpen = nextExpectedOpenTime - currentTime;

            if (timeToNextOpen.TotalSeconds > CacheValidThresholdSeconds)
            {
                _logger.LogTrace($"缓存有效：预计下次开放时间 {nextExpectedOpenTime:HH:mm:ss}，还有 {timeToNextOpen.TotalSeconds:F0} 秒");
                return true;
            }
        }

        _logger.LogTrace($"缓存失效：当前时间 {currentTime:HH:mm:ss} 已超过关闭时间 {cachedIssueTime.CloseTime:HH:mm:ss}，需要查询下一个时间段");
        return false;
    }

    #endregion
}
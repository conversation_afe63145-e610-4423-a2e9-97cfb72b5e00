﻿using CommandGuard.Enums;
using CommandGuard.Models;

namespace CommandGuard.Interfaces;

/// <summary>
/// 时间发放服务接口
/// 提供发放时间的创建、查询、删除等功能
/// </summary>
public interface IIssueTimeService
{
    /// <summary>
    /// 创建发放记录
    /// </summary>
    Task<IssueTime> CreateIssueTimeAsync(DateTime issueTimeYear);

    /// <summary>
    /// 维护更新当前发放记录
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task UpdateCurrentIssueTimeAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 线程安全地获取当前缓存的发放时间信息
    /// </summary>
    /// <returns>当前缓存的发放时间对象，如果缓存为空则返回null</returns>
    IssueTime? GetCurrentCachedIssueTime();

    /// <summary>
    /// 线程安全地获取当前缓存发放时间信息的副本
    /// </summary>
    /// <returns>当前缓存发放时间对象的副本，如果缓存为空则返回null</returns>
    IssueTime? GetCurrentCachedIssueTimeCopy();

    /// <summary>
    /// 检查缓存是否已初始化且有效
    /// </summary>
    /// <returns>如果缓存已初始化且有效返回true，否则返回false</returns>
    bool IsCacheInitialized();

    /// <summary>
    /// 线程安全地获取当前期号状态
    /// </summary>
    /// <returns>当前期号的状态枚举值</returns>
    IssueStatus GetCurrentIssueStatus();

    /// <summary>
    /// 线程安全地获取当前期号信息和状态
    /// </summary>
    /// <returns>包含期号信息和状态的元组</returns>
    (IssueTime? issueTime, IssueStatus status) GetCurrentIssueTimeAndStatus();

    /// <summary>
    /// 检查当前是否允许投注
    /// </summary>
    /// <returns>如果当前允许投注返回true，否则返回false</returns>
    bool CanBetNow();

    /// <summary>
    /// 强制更新当前状态
    /// </summary>
    void RefreshCurrentStatus();

    /// <summary>
    /// 清除缓存
    /// </summary>
    void ClearCache();
}
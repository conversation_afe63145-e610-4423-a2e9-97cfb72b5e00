﻿using CommandGuard.Enums;
using CommandGuard.Models;

namespace CommandGuard.Interfaces;

/// <summary>
/// 期号时间服务接口
///
/// 功能概述：
/// - 提供期号时间数据的创建、查询和管理功能
/// - 支持高性能的多线程安全缓存访问
/// - 实时维护期号状态信息
/// - 为投注业务提供状态判断支持
///
/// 核心特性：
/// - 自动生成全年期号数据（每天203期，每期5分钟）
/// - 智能缓存机制，减少数据库查询
/// - 实时状态计算和更新
/// - 线程安全的并发访问支持
/// - 状态变化自动日志记录
///
/// 业务价值：
/// - 为投注系统提供准确的时间控制
/// - 支持实时的投注开放/关闭判断
/// - 优化系统性能，提升用户体验
/// - 便于业务监控和问题排查
///
/// 资源管理：
/// - 实现IDisposable接口，支持资源自动释放
/// - 通过依赖注入容器管理生命周期
/// - 确保系统关闭时正确清理资源
/// </summary>
public interface IIssueTimeService : IDisposable
{
    /// <summary>
    /// 创建期号时间数据
    ///
    /// 功能：为指定年份生成完整的期号时间数据
    ///
    /// 生成规则：
    /// - 每天203期，从早上7点开始
    /// - 每期间隔5分钟，持续5分钟
    /// - 期号格式：民国年份+6位序号（如：113000001）
    /// - 自动处理闰年和平年的天数差异
    ///
    /// 使用场景：
    /// - 系统初始化时创建当年数据
    /// - 跨年时自动创建下一年数据
    /// - 数据重建或修复时使用
    ///
    /// 注意事项：
    /// - 如果指定年份数据已存在，直接返回第一条记录
    /// - 批量插入操作，性能优化
    /// - 支持异步操作，不阻塞主线程
    /// </summary>
    /// <param name="issueTimeYear">目标年份的任意日期</param>
    /// <returns>创建的第一条期号时间记录</returns>
    Task<IssueTime> CreateIssueTimeAsync(DateTime issueTimeYear);

    /// <summary>
    /// 维护更新当前期号时间信息
    ///
    /// 功能：后台持续运行的服务，负责维护当前期号缓存和状态
    ///
    /// 工作机制：
    /// - 每秒检查一次当前时间和期号状态
    /// - 智能缓存策略，减少数据库查询频率
    /// - 实时计算和更新期号状态
    /// - 自动处理期号切换和跨年数据创建
    ///
    /// 状态管理：
    /// - 根据当前时间自动计算期号状态
    /// - 状态变化时记录详细日志
    /// - 支持状态变化事件通知
    ///
    /// 异常处理：
    /// - 数据库连接异常时的重试机制
    /// - 状态计算异常时的恢复策略
    /// - 详细的错误日志记录
    ///
    /// 性能优化：
    /// - 缓存有效期智能判断
    /// - 读写锁优化并发性能
    /// - 最小化数据库访问频率
    /// </summary>
    /// <param name="cancellationToken">取消令牌，用于优雅停止服务</param>
    /// <returns>持续运行的异步任务</returns>
    Task UpdateCurrentIssueTimeAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 线程安全地获取当前缓存的期号时间信息
    ///
    /// 功能：高性能的期号信息读取接口
    ///
    /// 性能特点：
    /// - 使用读锁，支持多线程并发访问
    /// - 直接返回缓存对象引用，性能最优
    /// - 适合高频率的状态查询场景
    ///
    /// 使用场景：
    /// - 投注前的期号信息获取
    /// - UI界面的实时信息显示
    /// - 业务逻辑中的时间判断
    /// - 系统监控和状态检查
    ///
    /// 注意事项：
    /// - 返回的是对象引用，调用方不应修改返回的对象
    /// - 如果需要修改数据，请使用 GetCurrentCachedIssueTimeCopy()
    /// - 返回null表示缓存未初始化或系统正在启动
    ///
    /// 线程安全：
    /// - 读取操作完全线程安全
    /// - 不会阻塞其他读取操作
    /// - 只在写入时短暂阻塞
    /// </summary>
    /// <returns>当前缓存的期号时间对象，缓存为空时返回null</returns>
    IssueTime? GetCurrentCachedIssueTime();

    /// <summary>
    /// 线程安全地获取当前期号时间信息的深度副本
    ///
    /// 功能：返回期号信息的独立副本，调用方可安全修改
    ///
    /// 适用场景：
    /// - 需要修改期号数据进行计算的业务逻辑
    /// - 需要保存历史快照的场景
    /// - 对数据安全性要求极高的操作
    /// - 避免意外修改原始缓存数据
    ///
    /// 性能考虑：
    /// - 创建新对象有一定性能开销
    /// - 适合低频率调用的场景
    /// - 如果只是读取数据，建议使用 GetCurrentCachedIssueTime()
    ///
    /// 数据安全：
    /// - 返回完全独立的对象副本
    /// - 修改返回对象不会影响原始缓存
    /// - 确保数据隔离和一致性
    ///
    /// 线程安全：
    /// - 副本创建过程完全线程安全
    /// - 使用读锁保护，支持并发访问
    /// - 返回后的对象归调用方独占
    /// </summary>
    /// <returns>当前期号时间对象的深度副本，缓存为空时返回null</returns>
    IssueTime? GetCurrentCachedIssueTimeCopy();

    /// <summary>
    /// 检查期号缓存是否已初始化且有效
    ///
    /// 功能：快速检查系统是否已完成初始化
    ///
    /// 使用场景：
    /// - 系统启动后的状态检查
    /// - 业务操作前的前置条件验证
    /// - 健康检查和监控
    /// - 错误处理和用户提示
    ///
    /// 返回值说明：
    /// - true：缓存已初始化，系统可正常使用
    /// - false：缓存未初始化，系统可能正在启动或发生异常
    ///
    /// 性能特点：
    /// - 极快的响应速度，适合高频调用
    /// - 使用读锁，支持并发访问
    /// - 不涉及复杂计算，仅检查对象是否为null
    /// </summary>
    /// <returns>缓存已初始化且有效时返回true，否则返回false</returns>
    bool IsCacheInitialized();

    /// <summary>
    /// 线程安全地获取当前期号状态
    ///
    /// 功能：快速获取当前期号的实时状态信息
    ///
    /// 状态类型：
    /// - Unknown: 系统初始化中或异常状态
    /// - WaitingToOpen: 等待开放（距离开放时间较远）
    /// - PreOpening: 即将开放（距离开放时间很近，通常1分钟内）
    /// - Open: 投注开放中（可以正常投注）
    /// - PreClosing: 即将关闭（距离关闭时间很近，通常1分钟内）
    /// - Closed: 投注已关闭（等待下一期或开奖）
    /// - Maintenance: 系统维护中（所有功能暂停）
    ///
    /// 性能优势：
    /// - 直接返回预计算的状态值，无需实时计算
    /// - 使用读锁，支持高并发访问
    /// - 适合UI界面的高频状态查询
    ///
    /// 使用场景：
    /// - 投注按钮的启用/禁用控制
    /// - 状态指示器的颜色和文本更新
    /// - 业务逻辑的条件判断
    /// - 音效播放的触发条件
    /// </summary>
    /// <returns>当前期号的状态枚举值</returns>
    IssueStatus GetCurrentIssueStatus();

    /// <summary>
    /// 线程安全地获取当前期号信息和状态
    ///
    /// 功能：原子性地获取期号信息和状态，确保数据一致性
    ///
    /// 原子性保证：
    /// - 在同一个锁保护下获取期号信息和状态
    /// - 避免在获取期号和状态之间发生状态变化
    /// - 确保返回的数据完全匹配和一致
    ///
    /// 性能优势：
    /// - 一次锁操作获取两个相关数据
    /// - 减少锁的获取次数，提高性能
    /// - 避免多次调用的时间差问题
    ///
    /// 使用场景：
    /// - 需要同时使用期号信息和状态的业务逻辑
    /// - UI界面的综合信息显示
    /// - 复杂的业务判断和计算
    /// - 状态报告和日志记录
    ///
    /// 返回值说明：
    /// - issueTime: 当前期号信息，未初始化时为null
    /// - status: 当前状态，始终有有效值（最差为Unknown）
    /// </summary>
    /// <returns>包含期号信息和状态的元组</returns>
    (IssueTime? issueTime, IssueStatus status) GetCurrentIssueTimeAndStatus();

    /// <summary>
    /// 检查当前是否允许投注
    ///
    /// 功能：快速判断当前状态是否允许进行投注操作
    ///
    /// 判断逻辑：
    /// - Open状态：完全允许投注，正常投注时间
    /// - PreClosing状态：允许投注但建议提醒用户抓紧时间
    /// - 其他状态：不允许投注
    ///
    /// 业务价值：
    /// - 投注请求的快速前置验证
    /// - 投注按钮状态的实时控制
    /// - 减少无效投注请求的处理
    /// - 提升用户体验和系统性能
    ///
    /// 使用场景：
    /// - 投注按钮的启用/禁用
    /// - 投注API的前置检查
    /// - 用户界面状态更新
    /// - 投注流程的入口验证
    ///
    /// 性能特点：
    /// - 基于预计算状态的快速判断
    /// - 使用读锁，支持高并发调用
    /// - 适合高频率的状态检查
    /// </summary>
    /// <returns>当前允许投注时返回true，否则返回false</returns>
    bool CanBetNow();

    /// <summary>
    /// 强制刷新当前期号状态
    ///
    /// 功能：立即重新计算并更新当前期号状态
    ///
    /// 使用场景：
    /// - 手动状态刷新操作
    /// - 状态异常时的修复操作
    /// - 系统时间调整后的状态同步
    /// - 调试和测试时的状态控制
    ///
    /// 执行逻辑：
    /// - 获取当前缓存的期号信息
    /// - 基于当前时间重新计算状态
    /// - 更新内部状态缓存
    /// - 记录状态变化日志
    ///
    /// 注意事项：
    /// - 此操作需要获取写锁，会短暂阻塞其他操作
    /// - 不应频繁调用，正常情况下系统会自动更新状态
    /// - 主要用于异常情况的手动干预
    ///
    /// 线程安全：
    /// - 使用写锁保护，确保状态更新的原子性
    /// - 不会与其他状态更新操作冲突
    /// </summary>
    void RefreshCurrentStatus();

    /// <summary>
    /// 清除期号时间缓存
    ///
    /// 功能：清空当前缓存的期号信息和状态，强制下次查询时重新从数据库获取
    ///
    /// 执行操作：
    /// - 清空缓存的期号时间对象
    /// - 重置状态为Unknown
    /// - 记录缓存清除日志
    /// - 下次访问时触发数据库查询
    ///
    /// 使用场景：
    /// - 数据库数据更新后的缓存刷新
    /// - 系统异常后的状态重置
    /// - 手动数据刷新操作
    /// - 调试和测试时的状态清理
    ///
    /// 影响范围：
    /// - 清除后所有状态查询将返回Unknown
    /// - 下次UpdateCurrentIssueTimeAsync循环将重新加载数据
    /// - 可能导致短暂的服务不可用
    ///
    /// 注意事项：
    /// - 此操作会获取写锁，短暂阻塞其他操作
    /// - 清除后需要等待后台服务重新加载数据
    /// - 建议在系统维护时使用
    /// </summary>
    void ClearCache();
}
﻿using CommandGuard.Models;

namespace CommandGuard.Interfaces;

/// <summary>
/// 时间发放服务接口
/// 提供发放时间的创建、查询、删除等功能
/// </summary>
public interface IIssueTimeService
{
    /// <summary>
    /// 创建发放记录
    /// </summary>
    Task<IssueTime> CreateIssueTimeAsync(DateTime issueTimeYear);

    /// <summary>
    /// 维护更新当前发放记录
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task UpdateCurrentIssueTimeAsync(CancellationToken cancellationToken);
}